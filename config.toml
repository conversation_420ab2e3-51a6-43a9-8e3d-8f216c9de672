# VoiceReplyXBot 插件配置文件

[basic]
# 是否启用插件
enable = true
# 全局优先级设置 (0-99)，值越高优先级越高
priority = 50

[tts]
# TTS服务配置
base = "https://api.siliconflow.cn/v1"
api_key = "sk-gtbgqawhuaqidljxlajtbpeiauhhlfnawwqfdahpoxrjafny"  
model = "FunAudioLLM/CosyVoice2-0.5B"
voice = "speech:zhaer:cm45268da00cyjydni8ftat8t:ibuhnhhonevnovteltxn"
response_format = "mp3"

[chat]
# Chat服务配置
base = "https://api.llingfei.com/v1"
api_key = "sk-c8uQawWjLf897za_gQD4qiY6BXXpQPvMmQHGhqp-il7vd-MMoG3xF28O6DM"  # 请填入您的Chat API密钥
model = "hsdeepseek-chat"
temperature = 0.7
system_prompt = "你叫吒儿，性格参考<魔童降世>中的哪吒。"
user_prompt = "请回答以下问题：{question}"

[features]
# 功能开关
auto_cleanup = true  # 是否自动清理临时文件
retry_count = 3      # API请求重试次数
timeout = 30         # API请求超时时间（秒）

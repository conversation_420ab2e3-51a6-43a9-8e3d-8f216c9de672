# VoiceReplyXBot 插件配置文件示例
# 复制此文件为 config.toml 并修改相应配置

[basic]
# 是否启用插件
enable = true

# 全局优先级设置 (0-99)，值越高优先级越高
# 50 为中等优先级，适合大多数场景
priority = 50

[tts]
# TTS (文本转语音) 服务配置

# API基础URL - 硅基流动
base = "https://api.siliconflow.cn/v1"

# API密钥 - 请替换为您的实际密钥
# 获取方式：访问 https://siliconflow.cn/ 注册并获取API密钥
api_key = "your_tts_api_key_here"

# TTS模型名称
# 可选模型：
# - FunAudioLLM/CosyVoice2-0.5B (推荐)
# - 其他支持的模型请查看API文档
model = "FunAudioLLM/CosyVoice2-0.5B"

# 语音音色
# 可选音色：
# - FunAudioLLM/CosyVoice2-0.5B:diana (女声，温柔)
# - FunAudioLLM/CosyVoice2-0.5B:longxiaochun (女声，活泼)
# - 其他音色请查看API文档
voice = "FunAudioLLM/CosyVoice2-0.5B:diana"

# 音频输出格式
# 支持格式：mp3, wav, flac
response_format = "mp3"

[chat]
# Chat (对话) 服务配置

# API基础URL
# OpenAI官方：https://api.openai.com/v1
# 其他兼容服务商请修改此URL
base = "https://api.openai.com/v1"

# API密钥 - 请替换为您的实际密钥
# 获取方式：访问 https://openai.com/ 获取API密钥
api_key = "your_chat_api_key_here"

# 对话模型
# 推荐模型：
# - gpt-3.5-turbo (性价比高)
# - gpt-4 (质量更高，成本更高)
# - gpt-4-turbo (平衡性能和成本)
model = "gpt-3.5-turbo"

# 温度参数 (0.0-2.0)
# 0.0: 更确定性的回答
# 1.0: 平衡创造性和确定性
# 2.0: 更有创造性的回答
temperature = 0.7

# 系统提示词 - 定义AI的角色和行为
# 可以根据需要自定义AI的性格和回答风格
system_prompt = "你叫吒儿，性格参考<魔童降世>中的哪吒。请用简洁明了的语言回答问题，保持活泼可爱的语气。"

# 用户提示词模板
# {question} 会被替换为用户的实际问题
user_prompt = "请回答以下问题：{question}"

[features]
# 功能特性配置

# 是否自动清理临时文件
# true: 发送语音后立即删除临时文件
# false: 保留临时文件（需要手动清理）
auto_cleanup = true

# API请求重试次数
# 当API请求失败时的重试次数
retry_count = 3

# API请求超时时间（秒）
# 建议设置为30-60秒
timeout = 30

# 临时文件最大保留时间（小时）
# 定时清理任务会删除超过此时间的临时文件
max_file_age_hours = 3

[advanced]
# 高级配置（可选）

# 是否启用调试日志
debug_mode = false

# 语音文件大小限制（MB）
# 超过此大小的语音文件会被拒绝发送
max_voice_size_mb = 10

# 单次对话最大字符数
# 超过此长度的问题会被截断
max_question_length = 500

# 回答最大字符数
# 超过此长度的回答会被截断
max_answer_length = 1000

# 自定义临时目录路径
# 留空使用系统默认临时目录
# 示例："/tmp/voicereply" 或 "C:\\temp\\voicereply"
temp_dir = ""

[triggers]
# 触发词配置

# 支持的触发词列表
# 用户发送以这些词开头的消息会触发语音回答
trigger_words = ["语音", "voice", "说话"]

# 是否区分大小写
case_sensitive = false

# 是否支持触发词后直接跟问题（无分隔符）
# true: "语音你好" 会被识别
# false: 只识别 "语音 你好" 或 "语音+你好"
allow_direct_question = true

[limits]
# 使用限制配置

# 每用户每分钟最大请求次数
# 0 表示无限制
max_requests_per_minute = 5

# 每用户每天最大请求次数
# 0 表示无限制
max_requests_per_day = 100

# 是否启用使用统计
enable_usage_stats = true

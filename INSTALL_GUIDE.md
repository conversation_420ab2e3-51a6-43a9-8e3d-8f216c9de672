# VoiceReplyXBot 安装指南

## 📋 安装前准备

### 1. 环境要求

- Python 3.8+
- xxxbot 框架已安装并运行
- 网络连接（用于API调用）

### 2. 依赖包

确保已安装以下Python包：

```bash
pip install requests loguru tomllib
```

## 🚀 安装步骤

### 步骤 1：下载插件

将整个 `VoiceReplyXBot` 文件夹复制到 xxxbot 的 `plugins` 目录下：

```
xxxbot/
└── plugins/
    └── VoiceReplyXBot/
        ├── __init__.py
        ├── main.py
        ├── config.toml
        ├── README_XBot.md
        └── INSTALL_GUIDE.md
```

### 步骤 2：配置API密钥

编辑 `config.toml` 文件，配置您的API密钥：

```toml
[basic]
enable = true
priority = 50

[tts]
base = "https://api.siliconflow.cn/v1"
api_key = "your_tts_api_key_here"  # 替换为您的TTS API密钥
model = "FunAudioLLM/CosyVoice2-0.5B"
voice = "FunAudioLLM/CosyVoice2-0.5B:diana"
response_format = "mp3"

[chat]
base = "https://api.openai.com/v1"
api_key = "your_chat_api_key_here"  # 替换为您的Chat API密钥
model = "gpt-3.5-turbo"
temperature = 0.7
system_prompt = "你叫吒儿，性格参考<魔童降世>中的哪吒。"
user_prompt = "请回答以下问题：{question}"
```

### 步骤 3：适配xxxbot环境

由于不同的xxxbot环境可能有不同的API接口，您需要根据实际情况修改 `main.py` 文件中的导入语句：

```python
# 将这些临时定义替换为实际的xxxbot导入
from WechatAPIClient import WechatAPIClient
from utils.decorators import on_text_message, schedule
from utils.plugin_base import PluginBase
```

### 步骤 4：重启xxxbot

重启 xxxbot 或重新加载插件：

```bash
# 方法1：重启xxxbot
systemctl restart xxxbot

# 方法2：使用xxxbot的插件重载命令（如果支持）
/reload_plugins
```

## 🔧 配置说明

### API服务商配置

#### 硅基流动 (TTS)

1. 访问 [硅基流动官网](https://siliconflow.cn/)
2. 注册账号并获取API密钥
3. 将密钥填入 `config.toml` 的 `tts.api_key` 字段

#### OpenAI (Chat)

1. 访问 [OpenAI官网](https://openai.com/)
2. 获取API密钥
3. 将密钥填入 `config.toml` 的 `chat.api_key` 字段

### 自定义配置

#### 修改AI角色

编辑 `config.toml` 中的 `system_prompt`：

```toml
[chat]
system_prompt = "你是一个专业的客服助手，请礼貌地回答用户问题。"
```

#### 调整语音设置

```toml
[tts]
voice = "FunAudioLLM/CosyVoice2-0.5B:longxiaochun"  # 更换声音
response_format = "wav"  # 更换音频格式
```

#### 性能优化

```toml
[features]
auto_cleanup = true   # 自动清理临时文件
retry_count = 5       # 增加重试次数
timeout = 60          # 增加超时时间
```

## 🧪 测试插件

### 1. 检查插件状态

发送命令查看插件是否正常加载：

```
/plugins
```

### 2. 测试语音问答

发送测试消息：

```
语音+你好
语音 介绍一下你自己
语音今天天气怎么样
```

### 3. 查看日志

检查xxxbot日志，确认插件正常工作：

```bash
tail -f /path/to/xxxbot/logs/xxxbot.log | grep VoiceReplyXBot
```

## 🐛 故障排除

### 常见问题

#### 1. 插件无法加载

**症状**：插件列表中没有VoiceReplyXBot

**解决方案**：
- 检查文件路径是否正确
- 确认 `__init__.py` 文件存在且内容正确
- 查看xxxbot启动日志中的错误信息

#### 2. API密钥错误

**症状**：插件响应"请先配置API密钥"

**解决方案**：
- 检查 `config.toml` 中的API密钥是否正确
- 确认API密钥有足够的额度
- 测试API密钥是否有效

#### 3. 语音生成失败

**症状**：收到文字回复而不是语音

**解决方案**：
- 检查TTS API配置
- 确认网络连接正常
- 查看日志中的具体错误信息

#### 4. 权限问题

**症状**：无法创建临时文件

**解决方案**：
- 确认xxxbot有写入临时目录的权限
- 检查磁盘空间是否充足
- 修改 `main.py` 中的临时目录路径

### 日志分析

启用调试日志：

```python
# 在main.py中添加
logger.add("voicereply_debug.log", level="DEBUG")
```

## 📞 技术支持

如果遇到问题，请：

1. 查看本安装指南
2. 检查xxxbot官方文档
3. 提交Issue到项目仓库
4. 联系插件作者

## 🔄 更新插件

### 手动更新

1. 备份当前配置文件
2. 下载新版本插件
3. 替换插件文件（保留配置文件）
4. 重启xxxbot

### 配置迁移

如果配置文件格式有变化，请参考新版本的配置示例进行调整。

---

祝您使用愉快！🎉

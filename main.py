# VoiceReplyXBot - xxxbot插件版本
# 基于原VoiceReply插件重构，适配xxxbot插件系统

import json
import requests
import os
import time
import random
import tomllib
from loguru import logger

# 注意：以下导入需要根据实际的xxxbot环境调整
# from WechatAPIClient import WechatAPIClient
# from utils.decorators import *
# from utils.plugin_base import PluginBase

# 临时基类定义，实际使用时请替换为真实的xxxbot基类
class PluginBase:
    def __init__(self):
        pass

# 临时API客户端定义，实际使用时请替换为真实的xxxbot API客户端
class WechatAPIClient:
    async def send_text_message(self, wxid, content):
        """发送文本消息"""
        pass

    async def send_voice_message(self, wxid, voice_path, format="mp3"):
        """发送语音消息"""
        pass

# 临时装饰器定义，实际使用时请替换为真实的xxxbot装饰器
def on_text_message(priority=50):
    def decorator(func):
        func._message_handler = True
        func._priority = priority
        return func
    return decorator

def schedule(schedule_type, **kwargs):
    def decorator(func):
        func._schedule_handler = True
        func._schedule_type = schedule_type
        func._schedule_kwargs = kwargs
        return func
    return decorator


class VoiceReplyXBot(PluginBase):
    description = "语音问答插件：发送'语音+问题'、'语音 问题'或'语音问题'，机器人将以语音方式回答"
    author = "AI Assistant"
    version = "2.0.0"

    def __init__(self):
        super().__init__()

        # 获取配置文件路径
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")

        try:
            with open(config_path, "rb") as f:
                config = tomllib.load(f)

            # 读取基本配置
            basic_config = config.get("basic", {})
            self.enable = basic_config.get("enable", False)
            
            # 读取TTS配置
            tts_config = config.get("tts", {})
            self.tts_config = {
                "base": tts_config.get("base", "https://api.siliconflow.cn/v1"),
                "api_key": tts_config.get("api_key", ""),
                "model": tts_config.get("model", "FunAudioLLM/CosyVoice2-0.5B"),
                "voice": tts_config.get("voice", "FunAudioLLM/CosyVoice2-0.5B:diana"),
                "response_format": tts_config.get("response_format", "mp3")
            }
            
            # 读取Chat配置
            chat_config = config.get("chat", {})
            self.chat_config = {
                "base": chat_config.get("base", "https://api.openai.com/v1"),
                "api_key": chat_config.get("api_key", ""),
                "model": chat_config.get("model", "gpt-3.5-turbo"),
                "temperature": chat_config.get("temperature", 0.7),
                "system_prompt": chat_config.get("system_prompt", "你是一个友好的AI助手，请用简洁明了的语言回答问题。"),
                "user_prompt": chat_config.get("user_prompt", "请回答以下问题：{question}")
            }

            # 读取功能配置
            features_config = config.get("features", {})
            self.auto_cleanup = features_config.get("auto_cleanup", True)
            self.retry_count = features_config.get("retry_count", 3)
            self.timeout = features_config.get("timeout", 30)

            # 读取高级配置
            advanced_config = config.get("advanced", {})
            self.debug_mode = advanced_config.get("debug_mode", False)
            self.max_voice_size_mb = advanced_config.get("max_voice_size_mb", 10)
            self.max_question_length = advanced_config.get("max_question_length", 500)
            self.max_answer_length = advanced_config.get("max_answer_length", 1000)
            self.temp_dir = advanced_config.get("temp_dir", "/tmp")

            # 读取触发词配置
            triggers_config = config.get("triggers", {})
            self.trigger_words = triggers_config.get("trigger_words", ["语音"])
            self.case_sensitive = triggers_config.get("case_sensitive", False)
            self.allow_direct_question = triggers_config.get("allow_direct_question", True)

            # 读取限制配置
            limits_config = config.get("limits", {})
            self.max_requests_per_minute = limits_config.get("max_requests_per_minute", 5)
            self.max_requests_per_day = limits_config.get("max_requests_per_day", 100)
            self.enable_usage_stats = limits_config.get("enable_usage_stats", True)

            # 初始化使用统计
            self.usage_stats = {} if self.enable_usage_stats else None

            logger.info(f"[VoiceReplyXBot] 配置加载成功，插件状态: {'启用' if self.enable else '禁用'}")

        except Exception as e:
            logger.error(f"[VoiceReplyXBot] 加载配置文件失败: {str(e)}")
            self.enable = False
            # 使用默认配置
            self.tts_config = {
                "base": "https://api.siliconflow.cn/v1",
                "api_key": "",
                "model": "FunAudioLLM/CosyVoice2-0.5B",
                "voice": "FunAudioLLM/CosyVoice2-0.5B:diana",
                "response_format": "mp3"
            }
            self.chat_config = {
                "base": "https://api.openai.com/v1",
                "api_key": "",
                "model": "gpt-3.5-turbo",
                "temperature": 0.7,
                "system_prompt": "你是一个友好的AI助手，请用简洁明了的语言回答问题。",
                "user_prompt": "请回答以下问题：{question}"
            }
            # 默认功能配置
            self.auto_cleanup = True
            self.retry_count = 3
            self.timeout = 30

    async def async_init(self):
        """异步初始化"""
        logger.info(f"[VoiceReplyXBot] 插件异步初始化完成")

    async def on_enable(self, bot=None):
        """插件启用时调用"""
        logger.info(f"[VoiceReplyXBot] 插件已启用")

    async def on_disable(self):
        """插件禁用时调用"""
        logger.info(f"[VoiceReplyXBot] 插件已禁用")

    def check_usage_limit(self, user_id):
        """检查用户使用限制"""
        if not self.enable_usage_stats:
            return True

        current_time = time.time()
        current_date = time.strftime("%Y-%m-%d", time.localtime(current_time))

        if user_id not in self.usage_stats:
            self.usage_stats[user_id] = {
                "daily": {current_date: 0},
                "minute_requests": []
            }

        user_stats = self.usage_stats[user_id]

        # 检查每日限制
        if self.max_requests_per_day > 0:
            daily_count = user_stats["daily"].get(current_date, 0)
            if daily_count >= self.max_requests_per_day:
                return False

        # 检查每分钟限制
        if self.max_requests_per_minute > 0:
            # 清理超过1分钟的请求记录
            user_stats["minute_requests"] = [
                req_time for req_time in user_stats["minute_requests"]
                if current_time - req_time < 60
            ]

            if len(user_stats["minute_requests"]) >= self.max_requests_per_minute:
                return False

        return True

    def record_usage(self, user_id):
        """记录用户使用"""
        if not self.enable_usage_stats:
            return

        current_time = time.time()
        current_date = time.strftime("%Y-%m-%d", time.localtime(current_time))

        if user_id not in self.usage_stats:
            self.usage_stats[user_id] = {
                "daily": {current_date: 0},
                "minute_requests": []
            }

        user_stats = self.usage_stats[user_id]

        # 记录每日使用
        if current_date not in user_stats["daily"]:
            user_stats["daily"][current_date] = 0
        user_stats["daily"][current_date] += 1

        # 记录每分钟使用
        user_stats["minute_requests"].append(current_time)

        # 清理旧的每日记录（保留最近7天）
        cutoff_date = time.strftime("%Y-%m-%d", time.localtime(current_time - 7 * 24 * 3600))
        user_stats["daily"] = {
            date: count for date, count in user_stats["daily"].items()
            if date >= cutoff_date
        }

    def is_trigger_message(self, content):
        """检查消息是否为触发消息"""
        if not self.case_sensitive:
            content = content.lower()
            trigger_words = [word.lower() for word in self.trigger_words]
        else:
            trigger_words = self.trigger_words

        for trigger_word in trigger_words:
            # 检查 "触发词+" 格式
            if content.startswith(f"{trigger_word}+"):
                return True, content[len(trigger_word)+1:].strip()

            # 检查 "触发词 " 格式（有空格）
            if content.startswith(f"{trigger_word} "):
                return True, content[len(trigger_word)+1:].strip()

            # 检查直接跟问题的格式（如果启用）
            if self.allow_direct_question and content.startswith(trigger_word) and len(content) > len(trigger_word):
                return True, content[len(trigger_word):].strip()

        return False, ""

    def get_chat_response(self, question):
        """
        使用Chat模型获取回答
        :param question: 用户的问题
        :return: AI的回答文本
        """
        try:
            if not self.chat_config.get("api_key"):
                return "请先在config.toml中配置正确的Chat API密钥"

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.chat_config['api_key']}"
            }

            system_prompt = self.chat_config["system_prompt"]
            user_prompt = self.chat_config["user_prompt"].format(question=question)

            data = {
                "model": self.chat_config["model"],
                "messages": [
                    {
                        "role": "system",
                        "content": system_prompt
                    },
                    {
                        "role": "user",
                        "content": user_prompt
                    }
                ],
                "temperature": self.chat_config["temperature"]
            }

            for retry in range(self.retry_count):
                try:
                    response = requests.post(
                        f"{self.chat_config['base']}/chat/completions",
                        headers=headers,
                        json=data,
                        timeout=self.timeout
                    )
                    response.raise_for_status()
                    break
                except requests.RequestException as e:
                    if retry == self.retry_count - 1:
                        logger.error(f"[VoiceReplyXBot] Chat API请求失败，重试次数已用完: {e}")
                        return f"抱歉，回答问题时出现错误: {str(e)}"
                    logger.warning(f"[VoiceReplyXBot] Chat API请求重试 {retry + 1}/{self.retry_count}: {e}")
                    time.sleep(1)

            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    return result["choices"][0]["message"]["content"].strip()
                else:
                    return "抱歉，获取回答失败，API返回结果异常"
            else:
                logger.error(f"[VoiceReplyXBot] Chat API请求失败: {response.status_code} {response.text}")
                return f"抱歉，获取回答失败，API请求错误: {response.status_code}"

        except Exception as e:
            logger.error(f"[VoiceReplyXBot] 获取回答时出错: {e}")
            return f"抱歉，获取回答时发生错误: {str(e)}"

    def text_to_speech(self, text):
        """
        使用TTS模型将文本转换为语音
        :param text: 要转换的文本
        :return: 语音文件路径或None（如果转换失败）
        """
        try:
            if not self.tts_config["api_key"] or self.tts_config["api_key"] == "your_tts_api_key_here":
                logger.error("[VoiceReplyXBot] 未配置TTS API密钥")
                return None

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.tts_config['api_key']}"
            }

            data = {
                "model": self.tts_config["model"],
                "input": text,
                "voice": self.tts_config["voice"],
                "response_format": self.tts_config["response_format"]
            }

            logger.debug(f"[VoiceReplyXBot] TTS请求参数: {json.dumps(data)}")

            for retry in range(self.retry_count):
                try:
                    response = requests.post(
                        f"{self.tts_config['base']}/audio/speech",
                        headers=headers,
                        json=data,
                        timeout=self.timeout
                    )
                    response.raise_for_status()
                    break
                except requests.RequestException as e:
                    if retry == self.retry_count - 1:
                        logger.error(f"[VoiceReplyXBot] TTS API请求失败，重试次数已用完: {e}")
                        return None
                    logger.warning(f"[VoiceReplyXBot] TTS API请求重试 {retry + 1}/{self.retry_count}: {e}")
                    time.sleep(1)

            if response.status_code == 200:
                # 创建临时目录
                tmp_dir = self.temp_dir if self.temp_dir else "/tmp"
                if not os.path.exists(tmp_dir):
                    os.makedirs(tmp_dir, exist_ok=True)

                timestamp = int(time.time())
                random_str = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz', k=6))
                file_ext = self.tts_config["response_format"]
                voice_path = os.path.join(tmp_dir, f"reply_{timestamp}_{random_str}.{file_ext}")

                with open(voice_path, "wb") as f:
                    f.write(response.content)

                if os.path.getsize(voice_path) == 0:
                    logger.error("[VoiceReplyXBot] 下载的文件大小为0")
                    os.remove(voice_path)
                    return None

                logger.info(f"[VoiceReplyXBot] 语音生成完成: {voice_path}, 大小: {os.path.getsize(voice_path)/1024:.2f}KB")
                return voice_path

            else:
                logger.error(f"[VoiceReplyXBot] TTS API请求失败: {response.status_code} {response.text}")
                return None

        except Exception as e:
            logger.error(f"[VoiceReplyXBot] 文本转语音时出错: {e}")
            if 'voice_path' in locals() and os.path.exists(voice_path):
                try:
                    os.remove(voice_path)
                except Exception as clean_error:
                    logger.error(f"[VoiceReplyXBot] 清理失败的语音文件时出错: {clean_error}")
            return None

    @on_text_message(priority=50)
    async def handle_text_message(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return True  # 插件未启用，允许后续插件处理

        content = message.get("Content", "").strip()
        user_id = message.get("FromWxid", "")

        if self.debug_mode:
            logger.debug(f"[VoiceReplyXBot] 正在处理内容: {content}")

        # 检查是否为触发消息
        is_trigger, question = self.is_trigger_message(content)

        if is_trigger:
            logger.info(f"[VoiceReplyXBot] 处理语音问答: {content}")

            # 检查使用限制
            if not self.check_usage_limit(user_id):
                await bot.send_text_message(
                    user_id,
                    "您的使用次数已达到限制，请稍后再试。"
                )
                return False  # 阻止后续插件处理

            # 检查问题长度
            if len(question) > self.max_question_length:
                question = question[:self.max_question_length]
                logger.warning(f"[VoiceReplyXBot] 问题过长，已截断: {len(question)}")

            if not question:
                await bot.send_text_message(
                    user_id,
                    f"请在触发词后输入您的问题，例如：{self.trigger_words[0]}+你好"
                )
                return False  # 阻止后续插件处理

            # 记录使用
            self.record_usage(user_id)

            # 获取AI回答
            answer = self.get_chat_response(question)

            # 检查回答长度
            if len(answer) > self.max_answer_length:
                answer = answer[:self.max_answer_length] + "..."
                logger.warning(f"[VoiceReplyXBot] 回答过长，已截断: {len(answer)}")

            logger.info(f"[VoiceReplyXBot] AI回答: {answer[:100]}{'...' if len(answer) > 100 else ''}")

            # 生成语音回复
            voice_path = self.text_to_speech(answer)

            if voice_path:
                # 检查语音文件大小
                file_size_mb = os.path.getsize(voice_path) / (1024 * 1024)
                if file_size_mb > self.max_voice_size_mb:
                    logger.warning(f"[VoiceReplyXBot] 语音文件过大: {file_size_mb:.2f}MB")
                    if self.auto_cleanup:
                        os.remove(voice_path)
                    await bot.send_text_message(
                        user_id,
                        f"语音文件过大，这是文字回答：\n{answer}"
                    )
                    return False

                logger.info(f"[VoiceReplyXBot] 生成语音文件: {voice_path}, 大小: {file_size_mb:.2f}MB")

                # 发送语音消息
                await bot.send_voice_message(
                    user_id,
                    voice_path,
                    format=self.tts_config["response_format"]
                )

                # 清理临时文件
                if self.auto_cleanup:
                    try:
                        os.remove(voice_path)
                        if self.debug_mode:
                            logger.debug(f"[VoiceReplyXBot] 已清理临时语音文件: {voice_path}")
                    except Exception as e:
                        logger.warning(f"[VoiceReplyXBot] 清理语音文件失败: {e}")

                return False  # 阻止后续插件处理
            else:
                logger.warning("[VoiceReplyXBot] 语音生成失败")

                # 发送文本回复
                await bot.send_text_message(
                    user_id,
                    f"语音生成失败，这是文字回答：\n{answer}"
                )

                return False  # 阻止后续插件处理

        return True  # 允许后续插件处理

    @schedule('interval', minutes=30)
    async def cleanup_temp_files(self, bot: WechatAPIClient):
        """定时清理临时文件"""
        if not self.enable or not self.auto_cleanup:
            return

        try:
            tmp_dir = self.temp_dir if self.temp_dir else "/tmp"
            if not os.path.exists(tmp_dir):
                return

            current_time = time.time()
            max_age = getattr(self, 'max_file_age_hours', 3) * 60 * 60  # 默认3小时
            cleaned_count = 0

            # 支持的音频格式
            audio_extensions = [".mp3", ".wav", ".flac"]

            for filename in os.listdir(tmp_dir):
                if filename.startswith("reply_") and any(filename.endswith(ext) for ext in audio_extensions):
                    file_path = os.path.join(tmp_dir, filename)
                    try:
                        if current_time - os.path.getmtime(file_path) > max_age:
                            os.remove(file_path)
                            cleaned_count += 1
                            if self.debug_mode:
                                logger.debug(f"[VoiceReplyXBot] 清理过期文件: {filename}")
                    except Exception as e:
                        logger.warning(f"[VoiceReplyXBot] 清理文件失败 {file_path}: {e}")

            if cleaned_count > 0:
                logger.info(f"[VoiceReplyXBot] 定时清理完成，清理了 {cleaned_count} 个过期文件")

        except Exception as e:
            logger.error(f"[VoiceReplyXBot] 定时清理任务异常: {e}")

    def get_help_text(self, **kwargs):
        """
        获取插件帮助文本
        :return: 帮助文本
        """
        help_text = "🎤 VoiceReplyXBot 语音问答插件 🎤\n\n"
        help_text += "📝 使用方法：\n"

        # 动态生成触发词示例
        trigger_word = self.trigger_words[0] if self.trigger_words else "语音"
        help_text += f"• 发送 '{trigger_word}+您的问题' 获取AI的语音回答\n"
        help_text += f"• 发送 '{trigger_word} 您的问题' 获取AI的语音回答\n"
        if self.allow_direct_question:
            help_text += f"• 发送 '{trigger_word}您的问题' 获取AI的语音回答\n"
        help_text += "\n"

        help_text += "💡 示例：\n"
        help_text += f"• {trigger_word}+今天天气怎么样\n"
        help_text += f"• {trigger_word} 讲个笑话\n"
        if self.allow_direct_question:
            help_text += f"• {trigger_word}你好啊\n"
        help_text += "\n"

        help_text += "⚙️ 插件信息：\n"
        help_text += f"• 版本：{self.version}\n"
        help_text += f"• 作者：{self.author}\n"
        help_text += f"• 状态：{'启用' if self.enable else '禁用'}\n"
        help_text += f"• 优先级：50\n"
        help_text += f"• 支持的触发词：{', '.join(self.trigger_words)}\n"
        help_text += f"• TTS模型：{self.tts_config.get('model', 'N/A')}\n"
        help_text += f"• Chat模型：{self.chat_config.get('model', 'N/A')}\n\n"

        if self.enable_usage_stats:
            help_text += "📊 使用限制：\n"
            if self.max_requests_per_minute > 0:
                help_text += f"• 每分钟最多 {self.max_requests_per_minute} 次请求\n"
            if self.max_requests_per_day > 0:
                help_text += f"• 每天最多 {self.max_requests_per_day} 次请求\n"
            help_text += "\n"

        help_text += "⚠️ 注意：请先在config.toml中配置正确的API密钥"
        return help_text

    def get_status_info(self):
        """获取插件状态信息"""
        status = {
            "name": "VoiceReplyXBot",
            "version": self.version,
            "author": self.author,
            "enabled": self.enable,
            "tts_configured": bool(self.tts_config.get("api_key")),
            "chat_configured": bool(self.chat_config.get("api_key")),
            "trigger_words": self.trigger_words,
            "usage_stats_enabled": self.enable_usage_stats,
            "auto_cleanup": self.auto_cleanup,
            "debug_mode": self.debug_mode
        }
        return status

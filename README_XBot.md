# VoiceReplyXBot - xxxbot插件版本

> 这是基于原VoiceReply插件重构的xxxbot插件版本，完全适配xxxbot插件系统规范。

## 🌟 插件简介

VoiceReplyXBot是一个支持语音问答的xxxbot插件，用户可以通过发送"语音+问题"的形式获取AI的语音回答。插件使用现代化的TOML配置文件，支持xxxbot的装饰器系统和优先级控制。

## ✨ 功能特点

- 🎯 **完全适配xxxbot**：使用xxxbot插件系统规范开发
- 🎤 **语音问答**：支持文本到语音的转换
- 🤖 **AI对话**：使用OpenAI的GPT模型生成回答
- 🔊 **高质量语音**：使用硅基流动的语音合成服务
- ⚙️ **TOML配置**：现代化的配置文件格式
- 🔄 **自动重试**：内置错误处理和重试机制
- 🧹 **自动清理**：自动清理临时语音文件
- 📊 **优先级控制**：支持xxxbot的优先级系统

## 🚀 使用方法

### 触发格式

支持三种触发格式：

1. `语音+您的问题`
2. `语音 您的问题`  
3. `语音您的问题`

### 使用示例

```
语音+今天天气怎么样
语音 介绍一下你自己
语音讲个笑话
语音什么是人工智能
```

## 📁 插件结构

```
VoiceReplyXBot/
├── __init__.py      # 插件入口点
├── main.py          # 插件主要代码
├── config.toml      # 插件配置文件
└── README_XBot.md   # 插件说明文档
```

## ⚙️ 配置说明

插件使用TOML格式的配置文件，配置项说明如下：

### 基本配置

```toml
[basic]
enable = true     # 是否启用插件
priority = 50     # 全局优先级 (0-99)
```

### TTS配置

```toml
[tts]
base = "https://api.siliconflow.cn/v1"
api_key = ""      # TTS API密钥
model = "FunAudioLLM/CosyVoice2-0.5B"
voice = "FunAudioLLM/CosyVoice2-0.5B:diana"
response_format = "mp3"
```

### Chat配置

```toml
[chat]
base = "https://api.openai.com/v1"
api_key = ""      # Chat API密钥
model = "gpt-3.5-turbo"
temperature = 0.7
system_prompt = "你叫吒儿，性格参考<魔童降世>中的哪吒。"
user_prompt = "请回答以下问题：{question}"
```

### 功能配置

```toml
[features]
auto_cleanup = true   # 自动清理临时文件
retry_count = 3       # API请求重试次数
timeout = 30          # API请求超时时间
```

## 🔧 安装方法

1. 将整个 `VoiceReplyXBot` 文件夹复制到 xxxbot 的 `plugins` 目录下
2. 编辑 `config.toml` 文件，配置您的API密钥
3. 重启 xxxbot 或重新加载插件

## 🎯 xxxbot特性

### 消息处理装饰器

```python
@on_text_message(priority=50)
async def handle_text_message(self, bot: WechatAPIClient, message: dict):
    # 处理文本消息
```

### 优先级控制

- 插件默认优先级为50（中等优先级）
- 可在配置文件中设置全局优先级
- 支持xxxbot的阻塞机制

### 生命周期管理

```python
async def async_init(self):
    """异步初始化"""
    
async def on_enable(self, bot=None):
    """插件启用时调用"""
    
async def on_disable(self):
    """插件禁用时调用"""
```

## 🔍 工作流程

1. **消息接收**：监听文本消息
2. **格式检查**：检查是否为语音问答格式
3. **问题提取**：从消息中提取用户问题
4. **AI对话**：调用Chat API获取回答
5. **语音合成**：将回答转换为语音文件
6. **消息发送**：发送语音消息给用户
7. **文件清理**：自动清理临时语音文件

## ⚠️ 注意事项

1. **API密钥**：使用前请确保已正确配置API密钥
2. **网络环境**：确保服务器能够访问相关API服务
3. **文件权限**：确保有临时目录的读写权限
4. **响应时间**：语音生成可能需要一定时间，请耐心等待
5. **错误处理**：如果语音生成失败，插件会自动返回文字回答

## 🐛 故障排除

### 插件不响应

1. 检查插件是否启用 (`enable = true`)
2. 检查API密钥是否正确配置
3. 查看日志中的错误信息

### 语音生成失败

1. 检查TTS API密钥和配置
2. 检查网络连接
3. 查看API服务状态

### 配置文件错误

1. 检查TOML语法是否正确
2. 确保所有必需字段都已配置
3. 查看插件启动日志

## 📝 更新日志

### v2.0.0
- 完全重构为xxxbot插件
- 使用TOML配置文件
- 支持xxxbot装饰器系统
- 添加优先级控制
- 改进错误处理和日志记录
- 添加自动文件清理功能

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个插件！

## 📄 许可证

本项目基于原VoiceReply插件开发，遵循相同的开源许可证。

## 🙏 鸣谢

- [xxxbot](https://github.com/NanSsye/xbot) - 插件系统框架
- [VoiceReply](https://github.com/flyhunterl/VoiceReply) - 原始插件项目
- [dify-on-wechat](https://github.com/hanfangyuan4396/dify-on-wechat) - 基础框架
- [SearchMusic](https://github.com/Lingyuzhou111/SearchMusic) - 项目思路来源
